import cv2
import numpy as np
import argparse
import os

def nv12_to_bgr(input_path, output_path, width, height):
    """
    将 NV12 二进制文件转换为 BGR 格式的图片
    :param input_path: 输入的 NV12 文件路径
    :param output_path: 输出的 BGR 图片路径
    :param width: 图像宽度 (3840)
    :param height: 图像高度 (2160)
    """
    try:
        # 计算 NV12 文件预期大小
        expected_size = width * height * 3 // 2
        
        # 读取二进制数据
        with open(input_path, 'rb') as f:
            nv12_data = f.read()
        
        # 验证文件大小
        if len(nv12_data) != expected_size:
            raise ValueError(f"文件大小错误: 预期 {expected_size} 字节, 实际 {len(nv12_data)} 字节")
        
        # 转换为 numpy 数组并重塑形状
        nv12_array = np.frombuffer(nv12_data, dtype=np.uint8)
        bgr_image = nv12_array.reshape((height * 3 // 2, width,1))  # 关键步骤：NV12 格式的特殊形状
        bgr_image = cv2.cvtColor(bgr_image, cv2.COLOR_YUV2BGR_NV12)  # 关键步骤：颜色空间转换
        
        # 执行颜色空间转换
 
        # 保存结果
        cv2.imwrite(output_path, bgr_image)
        print(f"转换成功! 结果已保存至: {output_path}")
        
        return True
    
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 创建命令行参数解析器
    # def compute_iou(box1, box2):
    #     # 提取坐标
    #     x1_min, y1_min, x1_max, y1_max = box1
    #     x2_min, y2_min, x2_max, y2_max = box2
        
    #     # 计算交集区域的坐标
    #     inter_x_min = max(x1_min, x2_min)
    #     inter_y_min = max(y1_min, y2_min)
    #     inter_x_max = min(x1_max, x2_max)
    #     inter_y_max = min(y1_max, y2_max)
        
    #     # 计算交集面积（如果没有重叠，面积为0）
    #     inter_width = max(0, inter_x_max - inter_x_min)
    #     inter_height = max(0, inter_y_max - inter_y_min)
    #     intersection_area = inter_width * inter_height
        
    #     # 计算两个框各自的面积
    #     area_box1 = (x1_max - x1_min +1) * (y1_max - y1_min+1)
    #     area_box2 = (x2_max - x2_min+1) * (y2_max - y2_min+1)
        
    #     # 计算并集面积
    #     union_area = area_box1 + area_box2 - intersection_area
        
    #     # 计算IoU
    #     iou = intersection_area / union_area if union_area > 0 else 0
    #     return iou

    # # 你的边界框坐标
    # box1 = (1035.940918, 793.024841, 1217.963867, 1253.121704)
    # box2 = (834.372070, 779.389771, 1050.645264, 1070.495605)

    # iou_value = compute_iou(box1, box2)
    # print(f"IoU值为: {iou_value:.4f}")
    # quit()

    # print((3839 - 1722) / (1728 - 538), 1920/ 1080)
    # quit()

    # INV_SQRT_3 = 0.75355 * 1920 / 2
    # #INV_SQRT_3 = 0.839 * 1920 / 2
    # #INV_SQRT_3 = 1.9626  * 1920
    # print(np.arctan2(918- 1920 / 2,INV_SQRT_3) * 180 / np.pi)
    # quit()
    # imgdir =r"D:\Download\imgs"
    # for fn in os.listdir(imgdir):
    #     if fn.endswith(".jpg"):
    #         img = cv2.imread(os.path.join(imgdir,fn))
    #         img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    #         cv2.imwrite(os.path.join(imgdir,fn.replace(".jpg",".png")),img)
    # quit()
    width = 1920 
    height = 1080 
    srcdir = r"D:\imgs"
    dstdir = r"\imgs\rgb"

    for fn in os.listdir(srcdir):
        nv12_to_bgr(os.path.join(srcdir,fn), os.path.join(dstdir,fn.replace(".bin",".bmp")), width, height)